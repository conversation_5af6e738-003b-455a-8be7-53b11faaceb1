2025-07-02 09:44:46,145 INFO werkzeug: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-02 09:44:46,146 INFO werkzeug: [33mPress CTRL+C to quit[0m
2025-07-02 09:44:46,149 INFO werkzeug:  * Restarting with stat
2025-07-02 09:44:47,253 WARNING werkzeug:  * Debugger is active!
2025-07-02 09:44:47,257 INFO werkzeug:  * Debugger PIN: 118-264-892
2025-07-02 09:44:48,893 INFO werkzeug: 127.0.0.1 - - [02/Jul/2025 09:44:48] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-02 09:44:48,939 INFO werkzeug: 127.0.0.1 - - [02/Jul/2025 09:44:48] "GET /auth/login HTTP/1.1" 200 -
2025-07-02 09:44:48,960 INFO werkzeug: 127.0.0.1 - - [02/Jul/2025 09:44:48] "GET /static/css/style.css HTTP/1.1" 200 -
2025-07-02 09:44:49,086 INFO werkzeug: 127.0.0.1 - - [02/Jul/2025 09:44:49] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
