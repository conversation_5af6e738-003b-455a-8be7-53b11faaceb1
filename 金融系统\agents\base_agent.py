from abc import ABC, abstractmethod
from datetime import datetime, timedelta
import logging
from typing import List, Dict, Any


class BaseAgent(ABC):
    """Agent基类"""

    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"agent.{name}")
        self.is_active = True

    @abstractmethod
    def execute(self) -> Dict[str, Any]:
        """执行Agent任务"""
        pass

    def log_action(self, action: str, details: str = ""):
        """记录Agent操作日志"""
        self.logger.info(f"Agent {self.name}: {action} - {details}")
