from datetime import datetime, timedelta, date
from typing import List, Dict, Any
from decimal import Decimal
from .base_agent import BaseAgent
from dao.material_dao import MaterialDAO
from services.notification_service import NotificationService
import json


def convert_decimal(obj):
    """转换Decimal和date类型，用于JSON序列化"""
    if isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, (datetime, date)):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {k: convert_decimal(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_decimal(item) for item in obj]
    return obj


class DataAnalysisAgent(BaseAgent):
    """数据分析Agent"""

    def __init__(self):
        super().__init__("DataAnalysis")
        self.material_dao = MaterialDAO()
        self.notification_service = NotificationService()

    def execute(self) -> Dict[str, Any]:
        """执行数据分析"""
        results = {
            'usage_patterns': {},
            'cost_analysis': {},
            'trend_analysis': {},
            'anomalies': [],
            'status': 'success'
        }

        try:
            # 使用模式分析
            results['usage_patterns'] = self.analyze_usage_patterns()

            # 成本分析
            results['cost_analysis'] = self.analyze_costs()

            # 趋势分析
            results['trend_analysis'] = self.analyze_trends()

            # 异常检测
            results['anomalies'] = self.detect_anomalies()

            # 生成分析报告
            self.generate_analysis_report(results)

            self.log_action("数据分析完成", "生成使用模式、成本分析和趋势预测报告")

        except Exception as e:
            self.logger.error(f"数据分析失败: {str(e)}")
            results['error'] = str(e)
            results['status'] = 'failed'

        return results

    def analyze_usage_patterns(self) -> Dict[str, Any]:
        """分析使用模式"""
        try:
            # 按类别统计使用量
            category_sql = """
            SELECT m.category, COUNT(*) as request_count, SUM(mr.quantity) as total_quantity
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
            GROUP BY m.category
            ORDER BY total_quantity DESC
            """
            category_usage = self.material_dao.db.execute_query(category_sql)

            # 按科室统计使用量
            department_sql = """
            SELECT d.name as department_name, COUNT(*) as request_count, SUM(mr.quantity) as total_quantity
            FROM material_requests mr
            JOIN users u ON mr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
            GROUP BY d.id, d.name
            ORDER BY total_quantity DESC
            """
            department_usage = self.material_dao.db.execute_query(department_sql)

            # 按时间统计使用趋势
            monthly_sql = """
            SELECT DATE_FORMAT(created_at, '%Y-%m') as month, 
                   COUNT(*) as request_count, 
                   SUM(quantity) as total_quantity
            FROM material_requests
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
            GROUP BY DATE_FORMAT(created_at, '%Y-%m')
            ORDER BY month
            """
            monthly_usage = self.material_dao.db.execute_query(monthly_sql)

            return {
                'category_usage': category_usage,
                'department_usage': department_usage,
                'monthly_trend': monthly_usage
            }

        except Exception as e:
            self.logger.error(f"使用模式分析失败: {e}")
            return {}

    def analyze_costs(self) -> Dict[str, Any]:
        """成本分析"""
        try:
            # 按类别统计成本
            category_cost_sql = """
            SELECT m.category,
                   SUM(mr.quantity * m.price) as total_cost,
                   AVG(m.price) as avg_unit_price,
                   COUNT(DISTINCT mr.id) as request_count
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
            AND m.price > 0
            GROUP BY m.category
            ORDER BY total_cost DESC
            """
            category_costs = self.material_dao.db.execute_query(category_cost_sql)

            # 按科室统计成本
            department_cost_sql = """
            SELECT d.name as department_name,
                   SUM(mr.quantity * m.price) as total_cost,
                   COUNT(DISTINCT mr.id) as request_count
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            JOIN users u ON mr.user_id = u.id
            JOIN departments d ON u.department_id = d.id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
            AND m.price > 0
            GROUP BY d.id, d.name
            ORDER BY total_cost DESC
            """
            department_costs = self.material_dao.db.execute_query(department_cost_sql)

            # 月度成本趋势
            monthly_cost_sql = """
            SELECT DATE_FORMAT(mr.created_at, '%Y-%m') as month,
                   SUM(mr.quantity * m.price) as total_cost
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
            AND m.price > 0
            GROUP BY DATE_FORMAT(mr.created_at, '%Y-%m')
            ORDER BY month
            """
            monthly_costs = self.material_dao.db.execute_query(monthly_cost_sql)

            return {
                'category_costs': category_costs,
                'department_costs': department_costs,
                'monthly_cost_trend': monthly_costs
            }

        except Exception as e:
            self.logger.error(f"成本分析失败: {e}")
            return {}

    def analyze_trends(self) -> Dict[str, Any]:
        """趋势分析"""
        try:
            # 申请量趋势
            request_trend_sql = """
            SELECT DATE(created_at) as date, COUNT(*) as daily_requests
            FROM material_requests
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(created_at)
            ORDER BY date
            """
            request_trends = self.material_dao.db.execute_query(request_trend_sql)

            # 计算趋势方向
            if len(request_trends) >= 7:
                recent_avg = sum(item['daily_requests'] for item in request_trends[-7:]) / 7
                earlier_avg = sum(item['daily_requests'] for item in request_trends[:7]) / 7
                trend_direction = 'increasing' if recent_avg > earlier_avg else 'decreasing'
                trend_strength = abs(recent_avg - earlier_avg) / earlier_avg if earlier_avg > 0 else 0
            else:
                trend_direction = 'stable'
                trend_strength = 0

            # 热门物资趋势
            popular_materials_sql = """
            SELECT m.name, m.category, COUNT(*) as request_count
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY m.id, m.name, m.category
            ORDER BY request_count DESC
            LIMIT 10
            """
            popular_materials = self.material_dao.db.execute_query(popular_materials_sql)

            return {
                'request_trends': request_trends,
                'trend_direction': trend_direction,
                'trend_strength': round(trend_strength, 2),
                'popular_materials': popular_materials
            }

        except Exception as e:
            self.logger.error(f"趋势分析失败: {e}")
            return {}

    def detect_anomalies(self) -> List[Dict]:
        """异常检测"""
        anomalies = []
        
        try:
            # 检测异常高频申请用户
            high_frequency_sql = """
            SELECT u.username, u.real_name, COUNT(*) as request_count
            FROM material_requests mr
            JOIN users u ON mr.user_id = u.id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY u.id, u.username, u.real_name
            HAVING request_count > 10
            ORDER BY request_count DESC
            """
            high_freq_users = self.material_dao.db.execute_query(high_frequency_sql)
            
            for user in high_freq_users:
                anomalies.append({
                    'type': 'high_frequency_user',
                    'description': f"用户 {user['real_name']} 在过去7天内申请了{user['request_count']}次物资",
                    'severity': 'medium',
                    'data': user
                })

            # 检测异常大量申请
            large_quantity_sql = """
            SELECT mr.*, m.name as material_name, u.real_name
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            JOIN users u ON mr.user_id = u.id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            AND mr.quantity > 50
            ORDER BY mr.quantity DESC
            """
            large_requests = self.material_dao.db.execute_query(large_quantity_sql)
            
            for request in large_requests:
                anomalies.append({
                    'type': 'large_quantity_request',
                    'description': f"用户 {request['real_name']} 申请了大量 {request['material_name']} ({request['quantity']}个)",
                    'severity': 'high',
                    'data': request
                })

        except Exception as e:
            self.logger.error(f"异常检测失败: {e}")

        return anomalies

    def generate_analysis_report(self, results: Dict[str, Any]):
        """生成分析报告并发送通知"""
        try:
            # 构建报告摘要
            summary_parts = []
            
            if results.get('usage_patterns', {}).get('category_usage'):
                top_category = results['usage_patterns']['category_usage'][0]
                summary_parts.append(f"最活跃类别: {top_category['category']} ({top_category['total_quantity']}次申请)")
            
            if results.get('trend_analysis', {}).get('trend_direction'):
                trend = results['trend_analysis']['trend_direction']
                summary_parts.append(f"申请趋势: {trend}")
            
            if results.get('anomalies'):
                summary_parts.append(f"发现 {len(results['anomalies'])} 项异常情况")

            if summary_parts:
                summary = "数据分析报告摘要:\n\n" + "\n".join(f"• {part}" for part in summary_parts)
                
                # 转换Decimal类型以便JSON序列化
                serializable_results = convert_decimal(results)

                self.notification_service.send_admin_notification(
                    title="周度数据分析报告",
                    message=summary,
                    data=serializable_results
                )

        except Exception as e:
            self.logger.error(f"生成分析报告失败: {e}")
