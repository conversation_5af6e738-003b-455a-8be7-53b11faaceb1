from typing import List, Dict, Any
from .base_agent import BaseAgent
from dao.material_dao import MaterialDAO
from services.notification_service import NotificationService


class SmartRecommendationAgent(BaseAgent):
    """智能推荐Agent"""

    def __init__(self):
        super().__init__("SmartRecommendation")
        self.material_dao = MaterialDAO()
        self.notification_service = NotificationService()

    def execute(self) -> Dict[str, Any]:
        """执行推荐任务"""
        results = {
            'recommendations_generated': 0,
            'users_processed': 0,
            'status': 'success'
        }

        try:
            # 获取活跃用户
            active_users = self.get_active_users()
            results['users_processed'] = len(active_users)

            # 为每个用户生成推荐
            total_recommendations = 0
            for user in active_users:
                user_recommendations = self.generate_user_recommendations(user['id'])
                if user_recommendations:
                    self.save_recommendations(user['id'], user_recommendations)
                    total_recommendations += len(user_recommendations)

            results['recommendations_generated'] = total_recommendations

            self.log_action("推荐生成完成", 
                          f"处理用户:{len(active_users)}, 生成推荐:{total_recommendations}")

        except Exception as e:
            self.logger.error(f"推荐生成失败: {str(e)}")
            results['error'] = str(e)
            results['status'] = 'failed'

        return results

    def get_active_users(self) -> List[Dict]:
        """获取活跃用户（最近30天有申请记录）"""
        try:
            sql = """
            SELECT DISTINCT u.id, u.username, u.department_id, u.real_name
            FROM users u
            JOIN material_requests mr ON u.id = mr.user_id
            WHERE mr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            AND u.role = 'employee'
            """
            return self.material_dao.db.execute_query(sql)
        except Exception as e:
            self.logger.error(f"获取活跃用户失败: {e}")
            return []

    def generate_user_recommendations(self, user_id: int) -> List[Dict]:
        """为用户生成物资推荐"""
        try:
            # 获取用户历史申请
            user_history = self.get_user_request_history(user_id)
            if not user_history:
                return []

            # 获取同科室其他用户的热门申请
            department_recommendations = self.get_department_popular_materials(user_id)
            
            # 获取全局热门物资
            global_recommendations = self.get_global_popular_materials(user_id)

            # 合并推荐结果
            recommendations = []
            
            # 添加科室推荐
            for item in department_recommendations[:3]:
                recommendations.append({
                    'material_id': item['id'],
                    'material_name': item['name'],
                    'category': item['category'],
                    'reason': f"您的科室同事经常申请此物资",
                    'score': 0.8,
                    'type': 'department'
                })

            # 添加全局推荐
            for item in global_recommendations[:2]:
                recommendations.append({
                    'material_id': item['id'],
                    'material_name': item['name'],
                    'category': item['category'],
                    'reason': f"这是最近的热门申请物资",
                    'score': 0.6,
                    'type': 'global'
                })

            return recommendations[:5]  # 返回前5个推荐

        except Exception as e:
            self.logger.error(f"生成用户推荐失败: {e}")
            return []

    def get_user_request_history(self, user_id: int) -> List[Dict]:
        """获取用户申请历史"""
        try:
            sql = """
            SELECT DISTINCT m.id, m.name, m.category
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE mr.user_id = %s
            AND mr.created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
            """
            return self.material_dao.db.execute_query(sql, (user_id,))
        except Exception as e:
            self.logger.error(f"获取用户申请历史失败: {e}")
            return []

    def get_department_popular_materials(self, user_id: int) -> List[Dict]:
        """获取科室热门物资"""
        try:
            sql = """
            SELECT m.id, m.name, m.category, COUNT(*) as request_count
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            JOIN users u ON mr.user_id = u.id
            WHERE u.department_id = (SELECT department_id FROM users WHERE id = %s)
            AND mr.user_id != %s
            AND mr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            AND m.status = 'available'
            AND m.remaining_quantity > 0
            GROUP BY m.id, m.name, m.category
            ORDER BY request_count DESC
            LIMIT 5
            """
            return self.material_dao.db.execute_query(sql, (user_id, user_id))
        except Exception as e:
            self.logger.error(f"获取科室热门物资失败: {e}")
            return []

    def get_global_popular_materials(self, user_id: int) -> List[Dict]:
        """获取全局热门物资"""
        try:
            sql = """
            SELECT m.id, m.name, m.category, COUNT(*) as request_count
            FROM material_requests mr
            JOIN materials m ON mr.material_id = m.id
            WHERE mr.user_id != %s
            AND mr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            AND m.status = 'available'
            AND m.remaining_quantity > 0
            GROUP BY m.id, m.name, m.category
            ORDER BY request_count DESC
            LIMIT 5
            """
            return self.material_dao.db.execute_query(sql, (user_id,))
        except Exception as e:
            self.logger.error(f"获取全局热门物资失败: {e}")
            return []

    def save_recommendations(self, user_id: int, recommendations: List[Dict]):
        """保存推荐结果"""
        try:
            # 确保推荐表存在
            self._ensure_recommendations_table()
            
            # 清除用户旧的推荐
            delete_sql = "DELETE FROM user_recommendations WHERE user_id = %s"
            self.material_dao.db.execute_update(delete_sql, (user_id,))
            
            # 插入新推荐
            for rec in recommendations:
                insert_sql = """
                INSERT INTO user_recommendations 
                (user_id, material_id, reason, score, type, created_at)
                VALUES (%s, %s, %s, %s, %s, NOW())
                """
                self.material_dao.db.execute_insert(insert_sql, (
                    user_id,
                    rec['material_id'],
                    rec['reason'],
                    rec['score'],
                    rec['type']
                ))
                
        except Exception as e:
            self.logger.error(f"保存推荐失败: {e}")

    def _ensure_recommendations_table(self):
        """确保推荐表存在"""
        try:
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS user_recommendations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                material_id INT NOT NULL,
                reason TEXT,
                score DECIMAL(3,2) DEFAULT 0.5,
                type VARCHAR(50) DEFAULT 'general',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (material_id) REFERENCES materials(id) ON DELETE CASCADE,
                INDEX idx_user_id (user_id),
                INDEX idx_created_at (created_at)
            )
            """
            self.material_dao.db.execute_update(create_table_sql)
        except Exception as e:
            self.logger.error(f"创建推荐表失败: {e}")

    def get_user_recommendations(self, user_id: int, limit: int = 5) -> List[Dict]:
        """获取用户推荐"""
        try:
            sql = """
            SELECT ur.*, m.name as material_name, m.category, m.remaining_quantity
            FROM user_recommendations ur
            JOIN materials m ON ur.material_id = m.id
            WHERE ur.user_id = %s
            AND m.status = 'available'
            AND m.remaining_quantity > 0
            ORDER BY ur.score DESC, ur.created_at DESC
            LIMIT %s
            """
            return self.material_dao.db.execute_query(sql, (user_id, limit))
        except Exception as e:
            self.logger.error(f"获取用户推荐失败: {e}")
            return []
