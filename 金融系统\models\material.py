from datetime import datetime, date
import uuid

class Material:
    def __init__(self, id=None, asset_number=None, name=None, model=None, 
                 category=None, price=None, purchase_date=None, supplier=None,
                 purchase_amount=None, quantity=1, remaining_quantity=None,
                 status='available', scrap_reason=None, scrap_date=None,
                 description=None, created_at=None, updated_at=None):
        self.id = id
        self.asset_number = asset_number
        self.name = name
        self.model = model
        self.category = category  # 'fixed_asset' or 'consumable'
        self.price = price
        self.purchase_date = purchase_date
        self.supplier = supplier
        self.purchase_amount = purchase_amount
        self.quantity = quantity
        self.remaining_quantity = remaining_quantity if remaining_quantity is not None else quantity
        self.status = status  # 'in_use', 'scrapped', 'available'
        self.scrap_reason = scrap_reason
        self.scrap_date = scrap_date
        self.description = description
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
    
    def is_fixed_asset(self):
        """检查是否为固定资产"""
        return self.category == 'fixed_asset'
    
    def is_consumable(self):
        """检查是否为耗材"""
        return self.category == 'consumable'
    
    def is_available(self):
        """检查是否可用"""
        return self.status == 'available' and self.remaining_quantity > 0
    
    def generate_asset_number(self):
        """为固定资产生成唯一编号"""
        if self.is_fixed_asset() and not self.asset_number:
            # 格式：FA + 年份 + 月份 + 随机字符
            today = date.today()
            prefix = f"FA{today.year}{today.month:02d}"
            suffix = str(uuid.uuid4())[:8].upper()
            self.asset_number = f"{prefix}{suffix}"
        return self.asset_number
    
    def calculate_total_value(self):
        """计算总价值"""
        return self.price * self.quantity if self.price and self.quantity else 0
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'asset_number': self.asset_number,
            'name': self.name,
            'model': self.model,
            'category': self.category,
            'price': float(self.price) if self.price else None,
            'purchase_date': self.purchase_date.isoformat() if self.purchase_date else None,
            'supplier': self.supplier,
            'purchase_amount': float(self.purchase_amount) if self.purchase_amount else None,
            'quantity': self.quantity,
            'remaining_quantity': self.remaining_quantity,
            'status': self.status,
            'scrap_reason': self.scrap_reason,
            'scrap_date': self.scrap_date.isoformat() if self.scrap_date else None,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'total_value': self.calculate_total_value()
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建Material对象"""
        material = cls(
            id=data.get('id'),
            asset_number=data.get('asset_number'),
            name=data.get('name'),
            model=data.get('model'),
            category=data.get('category'),
            price=data.get('price'),
            purchase_date=data.get('purchase_date'),
            supplier=data.get('supplier'),
            purchase_amount=data.get('purchase_amount'),
            quantity=data.get('quantity', 1),
            remaining_quantity=data.get('remaining_quantity'),
            status=data.get('status', 'available'),
            scrap_reason=data.get('scrap_reason'),
            scrap_date=data.get('scrap_date'),
            description=data.get('description'),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at')
        )
        return material
