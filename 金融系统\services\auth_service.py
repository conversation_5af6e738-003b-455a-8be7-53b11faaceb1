from dao.user_dao import User<PERSON><PERSON>
from dao.department_dao import DepartmentDAO
from models.user import User
import hashlib

class AuthService:
    def __init__(self):
        self.user_dao = UserDAO()
        self.department_dao = DepartmentDAO()
    
    def authenticate(self, username, password):
        """用户认证"""
        # 简单的密码验证（实际项目中应该使用加密）
        user = self.user_dao.authenticate_user(username, password)
        return user
    
    def register_user(self, username, password, real_name, role='employee', 
                     department_id=None, email=None, phone=None):
        """注册新用户"""
        # 检查用户名是否已存在
        existing_user = self.user_dao.get_user_by_username(username)
        if existing_user:
            raise ValueError("用户名已存在")
        
        # 创建新用户
        user = User(
            username=username,
            password=password,  # 实际项目中应该加密
            real_name=real_name,
            role=role,
            department_id=department_id,
            email=email,
            phone=phone
        )
        
        return self.user_dao.create_user(user)
    
    def change_password(self, user_id, old_password, new_password):
        """修改密码"""
        user = self.user_dao.get_user_by_id(user_id)
        if not user:
            raise ValueError("用户不存在")
        
        if user.password != old_password:
            raise ValueError("原密码错误")
        
        return self.user_dao.update_password(user_id, new_password)
    
    def get_user_info(self, user_id):
        """获取用户信息"""
        user = self.user_dao.get_user_by_id(user_id)
        if user:
            # 获取科室信息
            if user.department_id:
                department = self.department_dao.get_department_by_id(user.department_id)
                user_info = user.to_dict()
                user_info['department_name'] = department.name if department else None
                return user_info
        return None
    
    def check_permission(self, user, required_role=None, department_id=None):
        """检查用户权限"""
        if not user or not user.is_active():
            return False
        
        # 检查角色权限
        if required_role:
            if required_role == 'admin' and not user.is_admin():
                return False
        
        # 检查科室权限（普通员工只能访问自己科室的数据）
        if department_id and not user.is_admin():
            if user.department_id != department_id:
                return False
        
        return True
    
    def get_accessible_departments(self, user):
        """获取用户可访问的科室列表"""
        if user.is_admin():
            # 管理员可以访问所有科室
            return self.department_dao.get_all_departments()
        else:
            # 普通员工只能访问自己的科室
            if user.department_id:
                department = self.department_dao.get_department_by_id(user.department_id)
                return [department] if department else []
            return []
