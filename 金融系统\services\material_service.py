from dao.material_dao import MaterialDAO
from dao.allocation_dao import AllocationDAO, RequestDAO
from dao.user_dao import UserDAO
from dao.department_dao import DepartmentDAO
from models.material import Material
from models.allocation import MaterialAllocation, MaterialRequest
from datetime import datetime, date

class MaterialService:
    def __init__(self):
        self.material_dao = MaterialDAO()
        self.allocation_dao = AllocationDAO()
        self.request_dao = RequestDAO()
        self.user_dao = UserDAO()
        self.department_dao = DepartmentDAO()
    
    def create_material(self, material_data, user):
        """创建物资"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以创建物资")
        
        material = Material.from_dict(material_data)
        return self.material_dao.create_material(material)
    
    def get_material_list(self, user, category=None, keyword=None, min_price=None, max_price=None, department_id=None):
        """获取物资列表"""
        if keyword or min_price or max_price:
            materials = self.material_dao.search_materials(keyword, category, min_price, max_price)
        elif category:
            materials = self.material_dao.get_materials_by_category(category)
        else:
            materials = self.material_dao.get_all_materials()

        # 如果指定了科室ID（仅管理员可用），按科室过滤
        if user.is_admin() and department_id:
            allocated_materials = self._get_department_materials(department_id)
            allocated_material_ids = {item['material_id'] for item in allocated_materials}
            materials = [m for m in materials if m.id in allocated_material_ids]
        # 如果是普通员工，只显示已分配给其科室的物资
        elif not user.is_admin():
            allocated_materials = self._get_department_materials(user.department_id)
            allocated_material_ids = {item['material_id'] for item in allocated_materials}
            materials = [m for m in materials if m.id in allocated_material_ids]

        return [material.to_dict() for material in materials]
    
    def get_material_detail(self, material_id, user):
        """获取物资详情"""
        material = self.material_dao.get_material_by_id(material_id)
        if not material:
            return None
        
        # 检查权限
        if not user.is_admin():
            # 普通员工只能查看本科室的物资
            allocated_materials = self._get_department_materials(user.department_id)
            allocated_material_ids = {item['material_id'] for item in allocated_materials}
            if material.id not in allocated_material_ids:
                raise PermissionError("无权查看此物资")
        
        return material.to_dict()
    
    def update_material(self, material_id, material_data, user):
        """更新物资信息"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以更新物资")
        
        material = self.material_dao.get_material_by_id(material_id)
        if not material:
            raise ValueError("物资不存在")
        
        # 更新物资信息
        for key, value in material_data.items():
            if hasattr(material, key):
                setattr(material, key, value)
        
        return self.material_dao.update_material(material)
    
    def scrap_material(self, material_id, reason, user):
        """报废物资"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以报废物资")
        
        material = self.material_dao.get_material_by_id(material_id)
        if not material:
            raise ValueError("物资不存在")
        
        if material.is_fixed_asset():
            # 固定资产报废需要审核
            return self.material_dao.scrap_material(material_id, reason)
        else:
            # 耗材直接标记为消耗
            return self.material_dao.update_remaining_quantity(material_id, -material.remaining_quantity)
    
    def allocate_material(self, material_id, department_id, user_id, quantity, notes, allocated_by):
        """分配物资"""
        if not self.user_dao.get_user_by_id(allocated_by).is_admin():
            raise PermissionError("只有管理员可以分配物资")
        
        material = self.material_dao.get_material_by_id(material_id)
        if not material:
            raise ValueError("物资不存在")
        
        if material.remaining_quantity < quantity:
            raise ValueError("物资数量不足")
        
        # 创建分配记录
        allocation = MaterialAllocation(
            material_id=material_id,
            department_id=department_id,
            user_id=user_id,
            allocated_by=allocated_by,
            quantity=quantity,
            notes=notes
        )
        
        # 更新物资剩余数量
        self.material_dao.update_remaining_quantity(material_id, -quantity)
        
        return self.allocation_dao.create_allocation(allocation)
    
    def request_material(self, material_id, quantity, reason, user):
        """申请物资"""
        material = self.material_dao.get_material_by_id(material_id)
        if not material:
            raise ValueError("物资不存在")
        
        if material.remaining_quantity < quantity:
            raise ValueError("物资数量不足")
        
        request = MaterialRequest(
            material_id=material_id,
            user_id=user.id,
            department_id=user.department_id,
            quantity=quantity,
            reason=reason
        )
        
        return self.request_dao.create_request(request)
    
    def get_allocation_history(self, user, department_id=None):
        """获取分配历史"""
        if user.is_admin():
            if department_id:
                return self.allocation_dao.get_allocations_by_department(department_id)
            else:
                return self.allocation_dao.get_all_allocations()
        else:
            # 普通员工只能查看自己的分配记录
            return self.allocation_dao.get_allocations_by_user(user.id)
    
    def get_pending_requests(self, user):
        """获取待审核申请"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以查看待审核申请")
        
        return self.request_dao.get_pending_requests()
    
    def approve_request(self, request_id, user, notes=None):
        """批准申请"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以批准申请")
        
        request = self.request_dao.get_request_by_id(request_id)
        if not request:
            raise ValueError("申请不存在")
        
        if not request.is_pending():
            raise ValueError("申请已处理")
        
        # 批准申请
        self.request_dao.approve_request(request_id, user.id, notes)
        
        # 自动创建分配记录
        return self.allocate_material(
            request.material_id,
            request.department_id,
            request.user_id,
            request.quantity,
            f"申请批准: {notes}" if notes else "申请批准",
            user.id
        )
    
    def reject_request(self, request_id, user, notes=None):
        """拒绝申请"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以拒绝申请")
        
        request = self.request_dao.get_request_by_id(request_id)
        if not request:
            raise ValueError("申请不存在")
        
        if not request.is_pending():
            raise ValueError("申请已处理")
        
        return self.request_dao.reject_request(request_id, user.id, notes)
    
    def _get_department_materials(self, department_id):
        """获取科室的物资分配记录"""
        return self.allocation_dao.get_allocations_by_department(department_id)
