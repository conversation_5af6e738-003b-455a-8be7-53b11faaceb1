import json
from datetime import datetime
from typing import List, Dict
from dao.database import db


class NotificationService:
    """通知服务"""

    def __init__(self):
        self.db = db

    def send_admin_notification(self, title: str, message: str, data: List[Dict] = None):
        """发送管理员通知"""
        try:
            # 获取所有管理员用户
            admin_users = self.db.execute_query(
                "SELECT id FROM users WHERE role = 'admin'"
            )
            
            for admin in admin_users:
                self._create_notification(
                    user_id=admin['id'],
                    title=title,
                    message=message,
                    notification_type='admin',
                    data=data
                )
        except Exception as e:
            print(f"发送管理员通知失败: {e}")

    def send_user_notification(self, user_id: int, title: str, message: str, data: Dict = None):
        """发送用户通知"""
        try:
            self._create_notification(
                user_id=user_id,
                title=title,
                message=message,
                notification_type='user',
                data=data
            )
        except Exception as e:
            print(f"发送用户通知失败: {e}")

    def _create_notification(self, user_id: int, title: str, message: str, 
                           notification_type: str = 'user', data: Dict = None):
        """创建通知记录"""
        try:
            # 检查notifications表是否存在，如果不存在则创建
            self._ensure_notifications_table()
            
            sql = """
            INSERT INTO notifications (user_id, title, message, type, data, is_read, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            self.db.execute_insert(sql, (
                user_id,
                title,
                message,
                notification_type,
                json.dumps(data, ensure_ascii=False) if data else None,
                False,
                datetime.now()
            ))
        except Exception as e:
            print(f"创建通知记录失败: {e}")

    def _ensure_notifications_table(self):
        """确保notifications表存在"""
        try:
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                type ENUM('user', 'admin', 'system') DEFAULT 'user',
                data JSON,
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
            """
            self.db.execute_update(create_table_sql)
        except Exception as e:
            print(f"创建notifications表失败: {e}")

    def get_user_notifications(self, user_id: int, limit: int = 20):
        """获取用户通知"""
        try:
            sql = """
            SELECT * FROM notifications 
            WHERE user_id = %s 
            ORDER BY created_at DESC 
            LIMIT %s
            """
            return self.db.execute_query(sql, (user_id, limit))
        except Exception as e:
            print(f"获取用户通知失败: {e}")
            return []

    def mark_notification_read(self, notification_id: int, user_id: int):
        """标记通知为已读"""
        try:
            sql = """
            UPDATE notifications 
            SET is_read = TRUE, updated_at = NOW() 
            WHERE id = %s AND user_id = %s
            """
            self.db.execute_update(sql, (notification_id, user_id))
        except Exception as e:
            print(f"标记通知已读失败: {e}")

    def get_unread_count(self, user_id: int) -> int:
        """获取未读通知数量"""
        try:
            sql = "SELECT COUNT(*) as count FROM notifications WHERE user_id = %s AND is_read = FALSE"
            result = self.db.execute_query_one(sql, (user_id,))
            return result['count'] if result else 0
        except Exception as e:
            print(f"获取未读通知数量失败: {e}")
            return 0
