/* 马卡龙配色方案 - 低饱和度 */
:root {
    --primary-color: #d4c5f9;      /* 淡紫色 */
    --secondary-color: #f8d7da;    /* 淡粉色 */
    --accent-color: #c3e9d0;       /* 淡绿色 */
    --warning-color: #fff3cd;      /* 淡黄色 */
    --danger-color: #f5c2c7;       /* 淡红色 */
    --info-color: #cff4fc;         /* 淡蓝色 */
    --success-color: #d1e7dd;      /* 淡绿色 */
    --light-color: #fefefe;        /* 纯白 */
    --dark-color: #6c757d;         /* 深灰色 */
    --text-color: #495057;         /* 文字颜色 */
    --border-color: #e9ecef;       /* 边框颜色 */
    --shadow-color: rgba(0,0,0,0.08);
    --nav-bg: #f8f4ff;            /* 导航栏背景 */
    --nav-text: #5a5a5a;          /* 导航栏文字 */
    --nav-hover: #e8deff;         /* 导航栏悬停 */
    --card-bg: #ffffff;           /* 卡片背景 */
}

/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏 */
.navbar {
    background-color: var(--nav-bg);
    padding: 1rem 0;
    box-shadow: 0 2px 10px var(--shadow-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid var(--border-color);
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    flex-wrap: nowrap;
}

.navbar-brand {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--nav-text);
    text-decoration: none;
    white-space: nowrap;
    flex-shrink: 0;
}

.navbar-nav {
    display: flex;
    list-style: none;
    gap: 1.2rem;
    margin: 0;
    padding: 0;
    flex-wrap: nowrap;
    overflow-x: auto;
}

.navbar-nav li {
    flex-shrink: 0;
}

.navbar-nav a {
    color: var(--nav-text);
    text-decoration: none;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-weight: 500;
    white-space: nowrap;
    font-size: 0.9rem;
}

.navbar-nav a:hover {
    background-color: var(--nav-hover);
    color: var(--text-color);
}

.user-info {
    color: var(--nav-text);
    display: flex;
    align-items: center;
    gap: 0.8rem;
    flex-shrink: 0;
    white-space: nowrap;
}

.user-info span {
    font-size: 0.9rem;
}

.user-info a {
    color: var(--nav-text);
    text-decoration: none;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.85rem;
}

.user-info a:hover {
    background-color: var(--nav-hover);
    color: var(--text-color);
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 1rem 0;
    box-shadow: 0 5px 15px var(--shadow-color);
    border: 1px solid var(--border-color);
}

.card-header {
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.card-title {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 1rem;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-success {
    background-color: var(--success-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-danger {
    background-color: var(--danger-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-info {
    background-color: var(--info-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 182, 193, 0.2);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(255, 182, 193, 0.1);
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 15px var(--shadow-color);
    border-left: 5px solid var(--primary-color);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-color);
    font-size: 1.1rem;
}

/* 消息提示 */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 10px;
    margin: 1rem 0;
    border-left: 5px solid;
}

.alert-success {
    background-color: rgba(152, 251, 152, 0.2);
    border-left-color: var(--accent-color);
    color: #2d5a2d;
}

.alert-error {
    background-color: rgba(255, 160, 122, 0.2);
    border-left-color: var(--danger-color);
    color: #8b2635;
}

.alert-warning {
    background-color: rgba(255, 228, 181, 0.2);
    border-left-color: var(--warning-color);
    color: #8b6914;
}

.alert-info {
    background-color: rgba(135, 206, 235, 0.2);
    border-left-color: var(--info-color);
    color: #1e3a5f;
}

/* 登录页面 */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-color);
}

.login-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 10px 30px var(--shadow-color);
    width: 100%;
    max-width: 400px;
}

.login-title {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-color);
    font-size: 2rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .navbar .container {
        max-width: 100%;
        padding: 0 15px;
    }

    .navbar-nav {
        gap: 0.8rem;
    }

    .navbar-nav a {
        padding: 0.3rem 0.6rem;
        font-size: 0.85rem;
    }

    .user-info {
        gap: 0.6rem;
    }

    .user-info span {
        font-size: 0.85rem;
    }

    .user-info a {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .navbar .container {
        flex-direction: column;
        gap: 1rem;
    }

    .navbar-nav {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
        text-align: center;
    }

    .user-info {
        flex-direction: row;
        justify-content: center;
    }
}
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .card {
        padding: 1rem;
    }
    
    .table {
        font-size: 0.875rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.d-flex { display: flex; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }
.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }
