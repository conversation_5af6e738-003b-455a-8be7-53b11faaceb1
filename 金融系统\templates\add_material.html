{% extends "base.html" %}

{% block title %}添加物资 - 金融企业物资管理系统{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">添加物资</h1>
        <p>请填写物资的基本信息</p>
    </div>
    
    <form method="POST">
        <div class="form-group">
            <label for="name" class="form-label">物资名称 *</label>
            <input type="text" id="name" name="name" class="form-control" required>
        </div>
        
        <div class="form-group">
            <label for="model" class="form-label">型号</label>
            <input type="text" id="model" name="model" class="form-control">
        </div>
        
        <div class="form-group">
            <label for="category" class="form-label">类别 *</label>
            <select id="category" name="category" class="form-control form-select" required>
                <option value="">请选择类别</option>
                <option value="fixed_asset">固定资产</option>
                <option value="consumable">耗材</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="price" class="form-label">单价 (元) *</label>
            <input type="number" id="price" name="price" class="form-control" step="0.01" min="0" required>
        </div>
        
        <div class="form-group">
            <label for="purchase_date" class="form-label">购入日期 *</label>
            <input type="date" id="purchase_date" name="purchase_date" class="form-control" required>
        </div>
        
        <div class="form-group">
            <label for="supplier" class="form-label">供应商</label>
            <input type="text" id="supplier" name="supplier" class="form-control">
        </div>
        
        <div class="form-group">
            <label for="purchase_amount" class="form-label">采购金额 (元)</label>
            <input type="number" id="purchase_amount" name="purchase_amount" class="form-control" step="0.01" min="0">
        </div>
        
        <div class="form-group">
            <label for="quantity" class="form-label">数量 *</label>
            <input type="number" id="quantity" name="quantity" class="form-control" min="1" value="1" required>
        </div>
        
        <div class="form-group">
            <label for="description" class="form-label">描述</label>
            <textarea id="description" name="description" class="form-control" rows="3"></textarea>
        </div>
        
        <div class="d-flex gap-2">
            <button type="submit" class="btn btn-success">添加物资</button>
            <a href="{{ url_for('material.material_list') }}" class="btn btn-secondary">取消</a>
        </div>
    </form>
</div>

<script>
// 自动计算采购金额
document.getElementById('price').addEventListener('input', calculatePurchaseAmount);
document.getElementById('quantity').addEventListener('input', calculatePurchaseAmount);

function calculatePurchaseAmount() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const quantity = parseInt(document.getElementById('quantity').value) || 0;
    const purchaseAmount = price * quantity;
    
    if (purchaseAmount > 0) {
        document.getElementById('purchase_amount').value = purchaseAmount.toFixed(2);
    }
}

// 设置默认购入日期为今天
document.getElementById('purchase_date').value = new Date().toISOString().split('T')[0];
</script>
{% endblock %}
