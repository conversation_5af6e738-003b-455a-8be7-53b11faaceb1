{% extends "base.html" %}

{% block title %}Agent监控面板{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1>Agent智能监控面板</h1>
                <p class="text-muted">实时监控Agent运行状态和执行历史</p>
            </div>
        </div>
    </div>

    <!-- Agent状态卡片 -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i> 库存预警Agent
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>状态:</strong> <span id="inventory-status" class="badge">-</span></p>
                    <p><strong>调度:</strong> 每日 08:00</p>
                    <p><strong>上次运行:</strong> <span id="inventory-last-run">-</span></p>
                    <button class="btn btn-sm btn-outline-primary" onclick="runAgent('InventoryAlert')">
                        <i class="fas fa-play"></i> 手动执行
                    </button>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb"></i> 智能推荐Agent
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>状态:</strong> <span id="recommendation-status" class="badge">-</span></p>
                    <p><strong>调度:</strong> 每日 22:00</p>
                    <p><strong>上次运行:</strong> <span id="recommendation-last-run">-</span></p>
                    <button class="btn btn-sm btn-outline-success" onclick="runAgent('SmartRecommendation')">
                        <i class="fas fa-play"></i> 手动执行
                    </button>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i> 数据分析Agent
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>状态:</strong> <span id="analysis-status" class="badge">-</span></p>
                    <p><strong>调度:</strong> 每周一 09:00</p>
                    <p><strong>上次运行:</strong> <span id="analysis-last-run">-</span></p>
                    <button class="btn btn-sm btn-outline-info" onclick="runAgent('DataAnalysis')">
                        <i class="fas fa-play"></i> 手动执行
                    </button>
                    <a href="/agent/analysis" class="btn btn-sm btn-info ml-2">
                        <i class="fas fa-chart-line"></i> 查看分析结果
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary" id="total-executions">{{ statistics.total_executions or 0 }}</h3>
                    <p class="text-muted">总执行次数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success" id="success-rate">{{ statistics.success_rate or 0 }}%</h3>
                    <p class="text-muted">成功率</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info" id="active-agents">{{ agent_status.total_agents or 0 }}</h3>
                    <p class="text-muted">活跃Agent</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning" id="scheduler-status">
                        {% if agent_status.scheduler_running %}运行中{% else %}已停止{% endif %}
                    </h3>
                    <p class="text-muted">调度器状态</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 执行历史 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history"></i> 执行历史
                        <button class="btn btn-sm btn-outline-secondary float-right" onclick="refreshHistory()">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Agent名称</th>
                                    <th>状态</th>
                                    <th>执行时间</th>
                                    <th>耗时(秒)</th>
                                    <th>结果</th>
                                </tr>
                            </thead>
                            <tbody id="execution-history">
                                {% for record in execution_history %}
                                <tr>
                                    <td>{{ record.agent_name }}</td>
                                    <td>
                                        {% if record.status == 'success' %}
                                            <span class="badge badge-success">成功</span>
                                        {% else %}
                                            <span class="badge badge-danger">失败</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ record.executed_at.strftime('%Y-%m-%d %H:%M:%S') if record.executed_at else '-' }}</td>
                                    <td>{{ "%.2f"|format(record.execution_time) if record.execution_time else '-' }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-info" onclick="showResult('{{ record.result|e }}')">
                                            查看详情
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 结果详情模态框 -->
<div class="modal fade" id="resultModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">执行结果详情</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <pre id="result-content"></pre>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载时更新状态
$(document).ready(function() {
    updateAgentStatus();
    setInterval(updateAgentStatus, 30000); // 每30秒更新一次
});

function updateAgentStatus() {
    $.get('/agent/api/status', function(data) {
        if (data.agents) {
            updateAgentCard('InventoryAlert', data.agents.InventoryAlert);
            updateAgentCard('SmartRecommendation', data.agents.SmartRecommendation);
            updateAgentCard('DataAnalysis', data.agents.DataAnalysis);
        }
    });
}

function updateAgentCard(agentName, agentData) {
    if (!agentData) return;

    const statusMap = {
        'registered': 'badge-secondary',
        'running': 'badge-warning',
        'completed': 'badge-success',
        'failed': 'badge-danger'
    };

    const statusText = {
        'registered': '已注册',
        'running': '运行中',
        'completed': '已完成',
        'failed': '失败'
    };

    // 修复前缀映射
    let prefix = '';
    if (agentName === 'InventoryAlert') {
        prefix = 'inventory';
    } else if (agentName === 'SmartRecommendation') {
        prefix = 'recommendation';
    } else if (agentName === 'DataAnalysis') {
        prefix = 'analysis';
    }

    $(`#${prefix}-status`)
        .removeClass('badge-secondary badge-warning badge-success badge-danger')
        .addClass(statusMap[agentData.status] || 'badge-secondary')
        .text(statusText[agentData.status] || agentData.status);

    $(`#${prefix}-last-run`).text(
        agentData.last_run ? new Date(agentData.last_run).toLocaleString() : '未运行'
    );
}

function runAgent(agentName) {
    if (confirm(`确定要手动执行 ${agentName} Agent吗？`)) {
        // 显示执行中状态
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 执行中...';
        button.disabled = true;

        $.post(`/agent/api/run/${agentName}`, function(data) {
            if (data.error) {
                alert('执行失败: ' + data.error);
            } else {
                alert('Agent执行成功！');
                // 刷新状态和历史
                updateAgentStatus();
                setTimeout(function() {
                    location.reload(); // 刷新页面以显示最新的执行历史
                }, 1000);
            }
        }).fail(function(xhr) {
            alert('执行失败: ' + (xhr.responseJSON ? xhr.responseJSON.error : '网络错误'));
        }).always(function() {
            // 恢复按钮状态
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}

function refreshHistory() {
    location.reload();
}

function showResult(result) {
    try {
        const formatted = JSON.stringify(JSON.parse(result), null, 2);
        $('#result-content').text(formatted);
    } catch (e) {
        $('#result-content').text(result);
    }
    $('#resultModal').modal('show');
}
</script>
{% endblock %}
