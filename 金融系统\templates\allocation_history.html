{% extends "base.html" %}

{% block title %}分配记录{% endblock %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">分配记录</h2>
            <p class="text-muted">查看物资分配历史记录</p>
        </div>
        
        <!-- 筛选器 -->
        <div class="filter-section">
            <form method="GET" class="filter-form">
                <div class="form-group">
                    <label for="department_id">科室筛选：</label>
                    <select name="department_id" id="department_id" class="form-control">
                        <option value="">全部科室</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if request.args.get('department_id')|int == dept.id %}selected{% endif %}>
                            {{ dept.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">筛选</button>
            </form>
        </div>
        
        {% if allocations %}
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>分配编号</th>
                        <th>物资名称</th>
                        <th>分配科室</th>
                        <th>分配用户</th>
                        <th>分配数量</th>
                        <th>分配时间</th>
                        <th>操作人</th>
                        <th>备注</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    {% for allocation in allocations %}
                    <tr>
                        <td>{{ allocation.id }}</td>
                        <td>{{ allocation.material_name }}</td>
                        <td>{{ allocation.department_name }}</td>
                        <td>{{ allocation.user_name or '科室公用' }}</td>
                        <td>{{ allocation.quantity }}</td>
                        <td>{{ allocation.allocated_at.strftime('%Y-%m-%d %H:%M') if allocation.allocated_at else '' }}</td>
                        <td>{{ allocation.allocated_by_name }}</td>
                        <td>{{ allocation.notes or '-' }}</td>
                        <td>
                            {% if allocation.status == 'active' %}
                                <span class="badge badge-success">正常</span>
                            {% elif allocation.status == 'returned' %}
                                <span class="badge badge-info">已归还</span>
                            {% elif allocation.status == 'scrapped' %}
                                <span class="badge badge-danger">已报废</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-history fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">暂无分配记录</h4>
            <p class="text-muted">当前没有物资分配记录</p>
        </div>
        {% endif %}
    </div>
</div>

<style>
.filter-section {
    padding: 1rem;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 0;
}

.filter-form {
    display: flex;
    align-items: end;
    gap: 1rem;
}

.filter-form .form-group {
    margin-bottom: 0;
    min-width: 200px;
}

.filter-form label {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
}

.badge {
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.badge-success {
    background-color: var(--success-color);
    color: var(--text-color);
}

.badge-info {
    background-color: var(--info-color);
    color: var(--text-color);
}

.badge-danger {
    background-color: var(--danger-color);
    color: var(--text-color);
}

.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-color);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--text-color);
}

.table td {
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table-responsive {
    border-radius: 0 0 15px 15px;
}
</style>
{% endblock %}
