{% extends "base.html" %}

{% block title %}物资详情 - 金融企业物资管理系统{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">物资详情</h1>
        <div class="d-flex gap-2">
            <a href="{{ url_for('material.material_list') }}" class="btn btn-secondary">返回列表</a>
            {% if user.role == 'admin' %}
            <a href="{{ url_for('material.allocate_material', material_id=material.id) }}" class="btn btn-primary">分配物资</a>
            {% endif %}
        </div>
    </div>
    
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
        <div>
            <h3>基本信息</h3>
            <table class="table">
                <tr>
                    <td><strong>资产编号</strong></td>
                    <td>{{ material.asset_number or '-' }}</td>
                </tr>
                <tr>
                    <td><strong>物资名称</strong></td>
                    <td>{{ material.name }}</td>
                </tr>
                <tr>
                    <td><strong>型号</strong></td>
                    <td>{{ material.model or '-' }}</td>
                </tr>
                <tr>
                    <td><strong>类别</strong></td>
                    <td>
                        {% if material.category == 'fixed_asset' %}
                            <span style="color: var(--primary-color); font-weight: bold;">固定资产</span>
                        {% else %}
                            <span style="color: var(--accent-color); font-weight: bold;">耗材</span>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td><strong>单价</strong></td>
                    <td>¥{{ "%.2f"|format(material.price) }}</td>
                </tr>
                <tr>
                    <td><strong>数量</strong></td>
                    <td>{{ material.quantity }}</td>
                </tr>
                <tr>
                    <td><strong>剩余数量</strong></td>
                    <td>{{ material.remaining_quantity }}</td>
                </tr>
                <tr>
                    <td><strong>状态</strong></td>
                    <td>
                        {% if material.status == 'available' %}
                            <span style="color: var(--accent-color);">可用</span>
                        {% elif material.status == 'in_use' %}
                            <span style="color: var(--warning-color);">在用</span>
                        {% else %}
                            <span style="color: var(--danger-color);">已报废</span>
                        {% endif %}
                    </td>
                </tr>
            </table>
        </div>
        
        <div>
            <h3>采购信息</h3>
            <table class="table">
                <tr>
                    <td><strong>购入日期</strong></td>
                    <td>{{ material.purchase_date if material.purchase_date else '-' }}</td>
                </tr>
                <tr>
                    <td><strong>供应商</strong></td>
                    <td>{{ material.supplier or '-' }}</td>
                </tr>
                <tr>
                    <td><strong>采购金额</strong></td>
                    <td>¥{{ "%.2f"|format(material.purchase_amount or 0) }}</td>
                </tr>
                <tr>
                    <td><strong>创建时间</strong></td>
                    <td>{{ material.created_at if material.created_at else '-' }}</td>
                </tr>
                <tr>
                    <td><strong>更新时间</strong></td>
                    <td>{{ material.updated_at if material.updated_at else '-' }}</td>
                </tr>
            </table>
            
            {% if material.description %}
            <h3>描述</h3>
            <p>{{ material.description }}</p>
            {% endif %}
        </div>
    </div>
    
    {% if user.role != 'admin' and material.remaining_quantity > 0 %}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">申请物资</h3>
        </div>
        
        <form method="POST" action="{{ url_for('material.request_material', material_id=material.id) }}">
            <div class="form-group">
                <label for="quantity" class="form-label">申请数量</label>
                <input type="number" id="quantity" name="quantity" class="form-control" 
                       min="1" max="{{ material.remaining_quantity }}" value="1" required>
            </div>
            
            <div class="form-group">
                <label for="reason" class="form-label">申请理由</label>
                <textarea id="reason" name="reason" class="form-control" rows="3" required></textarea>
            </div>
            
            <button type="submit" class="btn btn-warning">提交申请</button>
        </form>
    </div>
    {% endif %}
</div>
{% endblock %}
