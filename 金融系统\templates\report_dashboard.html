{% extends "base.html" %}

{% block title %}统计报表 - 金融企业物资管理系统{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">统计报表</h1>
        <p>查看物资统计信息和生成各类报表</p>
    </div>
    
    <!-- 统计概览 -->
    {% if statistics %}
    <div class="stats-grid">
        {% if statistics.material_stats %}
            {% for stat in statistics.material_stats %}
            <div class="stat-card">
                <div class="stat-number">{{ stat.total_count or 0 }}</div>
                <div class="stat-label">
                    {% if stat.category == 'fixed_asset' %}固定资产数量{% else %}耗材数量{% endif %}
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number">¥{{ "%.2f"|format(stat.total_value or 0) }}</div>
                <div class="stat-label">
                    {% if stat.category == 'fixed_asset' %}固定资产价值{% else %}耗材价值{% endif %}
                </div>
            </div>
            {% endfor %}
        {% endif %}
        
        {% if statistics.total_value %}
        <div class="stat-card">
            <div class="stat-number">¥{{ "%.2f"|format(statistics.total_value) }}</div>
            <div class="stat-label">总价值</div>
        </div>
        {% endif %}
        
        {% if statistics.total_materials %}
        <div class="stat-card">
            <div class="stat-number">{{ statistics.total_materials }}</div>
            <div class="stat-label">总物资数量</div>
        </div>
        {% endif %}
    </div>
    {% endif %}
    
    <!-- 分类统计图表 -->
    {% if category_stats %}
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">分类统计</h2>
        </div>
        
        <div id="statisticsChart"></div>
        
        <div class="stats-grid">
            {% for category, data in category_stats.items() %}
            <div class="stat-card">
                <div class="stat-number">{{ data.count }}</div>
                <div class="stat-label">
                    {% if category == 'fixed_asset' %}固定资产{% else %}耗材{% endif %}
                </div>
                <div style="font-size: 0.875rem; color: var(--dark-color); margin-top: 0.5rem;">
                    价值：¥{{ "%.2f"|format(data.value) }}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- 报表生成 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">报表生成</h2>
        </div>
        
        <div class="d-flex gap-2 mb-3">
            <a href="{{ url_for('report.material_report') }}" class="btn btn-primary">物资台账报表</a>
            <a href="{{ url_for('report.department_report') }}" class="btn btn-info">科室物资报表</a>
        </div>
        
        <h3>导出功能</h3>
        <div class="d-flex gap-2">
            <a href="{{ url_for('report.export_material_inventory') }}" class="btn btn-success">
                导出物资台账 (Excel)
            </a>
            <a href="{{ url_for('report.export_material_inventory', category='fixed_asset') }}" class="btn btn-success">
                导出固定资产台账 (Excel)
            </a>
            <a href="{{ url_for('report.export_material_inventory', category='consumable') }}" class="btn btn-success">
                导出耗材台账 (Excel)
            </a>
            <a href="{{ url_for('report.export_allocation_report') }}" class="btn btn-warning">
                导出分配报表 (Excel)
            </a>
        </div>
    </div>
    
    <!-- 科室统计（仅管理员） -->
    {% if user.role == 'admin' and statistics.department_stats %}
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">科室统计</h2>
        </div>
        
        <table class="table">
            <thead>
                <tr>
                    <th>科室名称</th>
                    <th>用户数量</th>
                    <th>分配物资数量</th>
                </tr>
            </thead>
            <tbody>
                {% for dept in statistics.department_stats %}
                <tr>
                    <td>{{ dept.name }}</td>
                    <td>{{ dept.user_count or 0 }}</td>
                    <td>{{ dept.allocation_count or 0 }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
</div>

<script>
// 初始化图表数据
document.addEventListener('DOMContentLoaded', function() {
    {% if category_stats %}
    const categoryStats = {{ category_stats | tojson }};
    if (categoryStats) {
        renderCategoryChart(categoryStats);
    }
    {% endif %}
});

function renderCategoryChart(data) {
    const chartContainer = document.getElementById('statisticsChart');
    if (!chartContainer) return;
    
    chartContainer.innerHTML = '';
    
    Object.keys(data).forEach((category, index) => {
        const item = data[category];
        const bar = document.createElement('div');
        bar.className = 'chart-bar';
        bar.style.cssText = `
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        `;
        
        const label = document.createElement('div');
        label.style.cssText = `
            width: 100px;
            text-align: right;
            margin-right: 1rem;
            font-size: 0.875rem;
        `;
        label.textContent = category === 'fixed_asset' ? '固定资产' : '耗材';
        
        const barContainer = document.createElement('div');
        barContainer.style.cssText = `
            flex: 1;
            height: 30px;
            background-color: #f0f0f0;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
        `;
        
        const barFill = document.createElement('div');
        const maxValue = Math.max(...Object.values(data).map(d => d.count));
        const percentage = maxValue > 0 ? (item.count / maxValue) * 100 : 0;
        barFill.style.cssText = `
            height: 100%;
            width: ${percentage}%;
            background: linear-gradient(135deg, ${category === 'fixed_asset' ? '#FFB6C1' : '#98FB98'}, ${category === 'fixed_asset' ? '#E6E6FA' : '#90EE90'});
            border-radius: 15px;
            transition: width 0.5s ease;
        `;
        
        const valueLabel = document.createElement('div');
        valueLabel.style.cssText = `
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.875rem;
            font-weight: bold;
        `;
        valueLabel.textContent = item.count;
        
        barContainer.appendChild(barFill);
        barContainer.appendChild(valueLabel);
        bar.appendChild(label);
        bar.appendChild(barContainer);
        chartContainer.appendChild(bar);
    });
}
</script>
{% endblock %}
