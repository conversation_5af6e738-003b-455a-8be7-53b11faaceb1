#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的任务调度器，不依赖外部包
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Callable, Any


class SimpleScheduler:
    """简单的任务调度器"""
    
    def __init__(self):
        self.jobs = []
        self.running = False
        self.thread = None
    
    def every(self, interval: int = 1):
        """创建调度任务"""
        return Job(interval, self)
    
    def add_job(self, job):
        """添加任务"""
        self.jobs.append(job)
    
    def run_pending(self):
        """运行待执行的任务"""
        now = datetime.now()
        for job in self.jobs:
            if job.should_run(now):
                try:
                    job.run()
                except Exception as e:
                    print(f"任务执行失败: {e}")
    
    def start(self):
        """启动调度器"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._run_loop, daemon=True)
        self.thread.start()
        print("简单调度器已启动")
    
    def stop(self):
        """停止调度器"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        print("简单调度器已停止")
    
    def _run_loop(self):
        """调度循环"""
        while self.running:
            self.run_pending()
            time.sleep(60)  # 每分钟检查一次


class Job:
    """调度任务"""
    
    def __init__(self, interval: int, scheduler: SimpleScheduler):
        self.interval = interval
        self.scheduler = scheduler
        self.job_func = None
        self.unit = None
        self.at_time = None
        self.last_run = None
        self.next_run = None
        self.weekday = None
    
    def day(self):
        """每天执行"""
        self.unit = 'days'
        return self
    
    def hour(self):
        """每小时执行"""
        self.unit = 'hours'
        return self
    
    def minute(self):
        """每分钟执行"""
        self.unit = 'minutes'
        return self
    
    def week(self):
        """每周执行"""
        self.unit = 'weeks'
        return self

    def monday(self):
        """每周一执行"""
        self.unit = 'weeks'
        self.weekday = 0  # 周一
        return self
    
    def at(self, time_str: str):
        """指定执行时间"""
        try:
            # 处理 "monday.09:00" 格式
            if '.' in time_str:
                day_part, time_part = time_str.split('.', 1)
                if day_part.lower() == 'monday':
                    self.weekday = 0
                    self.unit = 'weeks'
                time_str = time_part

            if ':' in time_str:
                hour, minute = map(int, time_str.split(':'))
                self.at_time = (hour, minute)
            else:
                # 处理分钟格式，如 ":30"
                if time_str.startswith(':'):
                    minute = int(time_str[1:])
                    self.at_time = (None, minute)
        except ValueError:
            print(f"时间格式错误: {time_str}")
        return self
    
    def do(self, job_func: Callable, *args, **kwargs):
        """设置要执行的函数"""
        self.job_func = lambda: job_func(*args, **kwargs)
        self._schedule_next_run()
        self.scheduler.add_job(self)
        return self
    
    def should_run(self, now: datetime) -> bool:
        """检查是否应该运行"""
        if not self.next_run:
            return False
        return now >= self.next_run
    
    def run(self):
        """执行任务"""
        if self.job_func:
            self.last_run = datetime.now()
            self.job_func()
            self._schedule_next_run()
    
    def _schedule_next_run(self):
        """计算下次运行时间"""
        now = datetime.now()
        
        if self.unit == 'minutes':
            self.next_run = now + timedelta(minutes=self.interval)
        elif self.unit == 'hours':
            if self.at_time and self.at_time[1] is not None:
                # 每小时的指定分钟
                next_hour = now.replace(minute=self.at_time[1], second=0, microsecond=0)
                if next_hour <= now:
                    next_hour += timedelta(hours=1)
                self.next_run = next_hour
            else:
                self.next_run = now + timedelta(hours=self.interval)
        elif self.unit == 'days':
            if self.at_time:
                # 每天的指定时间
                next_day = now.replace(
                    hour=self.at_time[0], 
                    minute=self.at_time[1], 
                    second=0, 
                    microsecond=0
                )
                if next_day <= now:
                    next_day += timedelta(days=1)
                self.next_run = next_day
            else:
                self.next_run = now + timedelta(days=self.interval)
        elif self.unit == 'weeks':
            if self.at_time:
                # 每周的指定时间
                target_weekday = self.weekday if self.weekday is not None else 0  # 默认周一
                days_ahead = target_weekday - now.weekday()
                if days_ahead <= 0:  # 如果今天是目标日期或已过，则安排到下周
                    days_ahead += 7

                next_week = now + timedelta(days=days_ahead)
                next_week = next_week.replace(
                    hour=self.at_time[0],
                    minute=self.at_time[1],
                    second=0,
                    microsecond=0
                )

                # 如果计算出的时间已经过了，安排到下周
                if next_week <= now:
                    next_week += timedelta(weeks=1)

                self.next_run = next_week
            else:
                self.next_run = now + timedelta(weeks=self.interval)


# 创建全局调度器实例
schedule = SimpleScheduler()


# 兼容性函数
def run_pending():
    """运行待执行任务"""
    schedule.run_pending()


def every(interval: int = 1):
    """创建调度任务"""
    return schedule.every(interval)
