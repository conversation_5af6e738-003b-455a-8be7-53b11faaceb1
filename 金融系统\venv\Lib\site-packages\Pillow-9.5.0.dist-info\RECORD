PIL/BdfFontFile.py,sha256=7tb9V0PYmpXMySmPPMiScnkMPoFvMAd1Oez8houmWUM,3359
PIL/BlpImagePlugin.py,sha256=-nNUlAswu3S33X5-rkJXTWFQARz1r1oZeuZq3OeZVn8,16483
PIL/BmpImagePlugin.py,sha256=XOHw5_ZerT7vpH3djZvARU6ARiOUOWwVgp-6vpfua48,18138
PIL/BufrStubImagePlugin.py,sha256=Pc-ukDILRS2Knqtogyumr74CwI1-f5w9GChmkEgXiD0,1629
PIL/ContainerIO.py,sha256=NmRN9naqGy3KuR9nup6o1ihJRStbBHk-vyNpJb6oQF8,3003
PIL/CurImagePlugin.py,sha256=4DOiIo2AhLI95foB8E0DWhnomyEwWwEtSAP5wgWxyHM,1796
PIL/DcxImagePlugin.py,sha256=RwMLnvYc_CsQVgRYABQr7afzRyuGL0RfyH5PFo0dMC0,2037
PIL/DdsImagePlugin.py,sha256=fuih_I4DEoGtwbJ1FezlFgmGJg3yzJSF3PAvnn-Jisk,9637
PIL/EpsImagePlugin.py,sha256=P5nvS5R1HrHCOEHRwKyXbGkP21xvW7h_Ue2lQeFjy_8,15413
PIL/ExifTags.py,sha256=6HINw53r9dPBMb9d7B43zZ-gNtrMRqoRk3LthY_FDpo,10098
PIL/FitsImagePlugin.py,sha256=fBOm3G8ldckUuMw0qrYBwFHdtrytQ0nYutWEVD4KlhI,2132
PIL/FitsStubImagePlugin.py,sha256=iG1ma5iu38bIdqyqfWzCAAY4dyBilmZbj4bXtgQ0OJ8,1749
PIL/FliImagePlugin.py,sha256=S1Ein3U7MEE6MyM-V2opkCG5MQRQzAFYPckP9mLC40s,4614
PIL/FontFile.py,sha256=btTE3c7rQJXwM9Ndz3_IV9Q-h2sM2Bj3t_4UuoQld34,2874
PIL/FpxImagePlugin.py,sha256=j4wEE1BGKTDiR15Ww_yoHRqhQ6zZ3v0zzlFkNW_HSkk,7214
PIL/FtexImagePlugin.py,sha256=BK5WxU9t_CY7Gj_FYSWncm1hEUlu169OoQiul_rsog0,3980
PIL/GbrImagePlugin.py,sha256=6xofx5s2ghUqtbYLjpERIZR_73RGC78xbTL0UcX7ggU,3010
PIL/GdImageFile.py,sha256=aPaS1uHNdlsLrdCMw58I3iB_Su8fR9ZHQ1HiLfWFW1E,2704
PIL/GifImagePlugin.py,sha256=vINF0Y9qGxREdUCetlhTVUF_rNurTwJg8MpyEDAuN58,36797
PIL/GimpGradientFile.py,sha256=gmeJv2hu0EegYmkoN7ULuf0HPtj6PPqV1F-sPUsruco,3533
PIL/GimpPaletteFile.py,sha256=IfG8kYjL76X3sK7Sxr9DFz7FrSaSrebWYNid8bdBIaQ,1401
PIL/GribStubImagePlugin.py,sha256=txA0r1XgUhIjXnTOqxC8DbhvzpdN1IZsGw8IfJVO9gs,1623
PIL/Hdf5StubImagePlugin.py,sha256=MIxZmNNV9plZcKgNapzrIUnG5ob_tKjCOBT8Z3N50FU,1626
PIL/IcnsImagePlugin.py,sha256=9FOF3-_t30DR3A-JcSPiPq3kLLrbQUPNQ0gJ5vu5Adw,12329
PIL/IcoImagePlugin.py,sha256=nGeXfmEDX8VxXNVVNzZ4fzpaBWv9it2jMLo81bqzD50,11980
PIL/ImImagePlugin.py,sha256=0AieXMOlie5qH5psVJOHYlbzDlgED1wFqYubSGLbX6w,11238
PIL/Image.py,sha256=LXwA6imgpFEWkWovkijlxDtofR7cnGDgRDkpz3uIOEA,137510
PIL/ImageChops.py,sha256=xfHglbl9ENPlsehI9tBeiod1lVUy_5AKN949WL2uRlM,7306
PIL/ImageCms.py,sha256=boeqsW0-3vG6NOUrDx54blotUpsVQOxY27Js5Ms3zFg,38772
PIL/ImageColor.py,sha256=TJUOQu-ydwq_v9t6-hoIOCye2AnYiCduP8sz4uzrLu0,9097
PIL/ImageDraw.py,sha256=_Txn89wfgLteL9MdM08ONZzmJBI8UsNyf6oqQvyDhrk,39813
PIL/ImageDraw2.py,sha256=qvNpBIQlq_TP3dyLNTb8rjB-QtY1rWbXq9z0TAaVYZo,6210
PIL/ImageEnhance.py,sha256=tHMwYy_knQ_tice_c5MzDShwkFVL5DyJ1EVm1KHiDDI,3293
PIL/ImageFile.py,sha256=tieTgFwl9L15kiKyOE7syh0jjOARZe2wp1MAi-XJJdU,24312
PIL/ImageFilter.py,sha256=2vR-Mkyfsa1CGqR6MYPxis1Wa_nPzxvkHz9HfGzpaQU,17019
PIL/ImageFont.py,sha256=krZbfy55Q0X21z9DUEeEZuCpEsjfHkGWNDnuPctMV_0,51659
PIL/ImageGrab.py,sha256=Cj4Ivg2BQg_ut7q5Mc8Z53AvKCgANM4QJ_c_8lmMgq0,4834
PIL/ImageMath.py,sha256=oyM5Sjlu0cV7y2yc_G_axId3Dp4DaOKUTZ7frtNsweE,7620
PIL/ImageMode.py,sha256=VAdsoGIHDNkQeg5FU92bf3ROHIA9KK2J7HQpgLWyyls,3004
PIL/ImageMorph.py,sha256=JrTMGN0WyGjur-We0qmGl0Pb_S_F60s4ADPGvrmOH2c,8231
PIL/ImageOps.py,sha256=xcGldbwrB5MZxAUGXFngGIa6oKkTs6dF8uh8uMTGs9U,21590
PIL/ImagePalette.py,sha256=KdFQfozTSdonshM0DzCs6wFxjCkDlULe1Lta6QSrpic,8421
PIL/ImagePath.py,sha256=IZ7vxarm_tI2DuV7QPchZe1P4U52ypHbO-v3JkcGm44,355
PIL/ImageQt.py,sha256=r11fHtTzC9j0D_jL8Q0FM1odqNCWMk3APnye83qdfvw,7119
PIL/ImageSequence.py,sha256=4Rn6Jx4fc2oNu3vKUCjMhCEBwBoaRfExmrJGpjrJmHI,1948
PIL/ImageShow.py,sha256=4P0JaQgIN0vJKPQHw3cgBMH9iJ4EMDKQ0iOOAcMi_J8,11813
PIL/ImageStat.py,sha256=Tr4x_f_wE6wM2l6RtSmPxWXjkbtD63HSrHAKeR1j9Ps,4072
PIL/ImageTk.py,sha256=h6oVY8cQdQN-1qM8wGDCELGx4pJR_X2zqYa7aK0DxBo,8988
PIL/ImageTransform.py,sha256=EsgO8FV2Gnm1hBMVx-8i7I3bhehRfMlwHIsV7QQ7FjM,2985
PIL/ImageWin.py,sha256=qklIa-nlezkM_BUVRaIOgjSqRDWjQq8Qxe_r3sQy3Ro,7421
PIL/ImtImagePlugin.py,sha256=NbxZVUDuWSFTKt2RMyPufE5niVtLDLnGb-xjWIyg4io,2680
PIL/IptcImagePlugin.py,sha256=ryKOSjjK73lQfJoP5b52wdE4yNHwcH7eH_Be3j2LiYE,6007
PIL/Jpeg2KImagePlugin.py,sha256=6pLs8Hslm2fFFXwV4YEviP-scd7JBSWUHmct-RZIP9Y,11982
PIL/JpegImagePlugin.py,sha256=KmiY6xdRsAxo3ZfaXz3ksQ6Ol4Bn270rwi-jsnPwX-0,30012
PIL/JpegPresets.py,sha256=7lEumxxIdQrdc4Eync4R8IItvu7WyP6KKY9vaR3RHfY,12583
PIL/McIdasImagePlugin.py,sha256=x9p4xEMhso9z2H8wMlxg_pFJ4OxIEGnq_7tAgxe4-NE,1871
PIL/MicImagePlugin.py,sha256=TiJbCd4YFelTSlZq3juzU8bzQRinEcDcUFCLfoT2rD0,2699
PIL/MpegImagePlugin.py,sha256=hDlxjupy7q-55sGCJfTQCZU3QBcd72SA34Nmn3s4RDQ,1905
PIL/MpoImagePlugin.py,sha256=uhDiuxP_j7teKZwTFkDh7eF93iofseoVhB8hDvQiCjE,6486
PIL/MspImagePlugin.py,sha256=-7WU5orL01Z1hlYBx6Uou-87hl_1jOz8qUGQ8VTfmpA,5806
PIL/PSDraw.py,sha256=rxUa015jdjOMgQFsIi8DPaHxCNaBN-l0FIWMB03Let0,6754
PIL/PaletteFile.py,sha256=JuiaGSo0Kt7P_R2alJ4iALW93cTM_J7KLAdqgZwWyrQ,1179
PIL/PalmImagePlugin.py,sha256=vVD0dymsz8RLhavbcua_x6z4VvpWyG_UqBfeZqno3EM,9369
PIL/PcdImagePlugin.py,sha256=x6_E8hPLAPy70VxPg6SKUi2xDT8OhgiSIZx0pXdNDhw,1558
PIL/PcfFontFile.py,sha256=sRkDm6QE_7iByrCJABM8BRu8D3xrxX-nudj0tZfFT6M,7013
PIL/PcxImagePlugin.py,sha256=HTh7xqcQBpOo7GK_NEEDCeDbb0Cqge6mwA_vpWKWDk0,6242
PIL/PdfImagePlugin.py,sha256=Q-vtat694jtAFQiuMi_5aCD8r_o_92XY2oHhj1sbZmo,9264
PIL/PdfParser.py,sha256=mtgy10DcGk-1HLSbDzZY8OO-fsNniTVg5x-5kW2n5Po,35563
PIL/PixarImagePlugin.py,sha256=uYA7SlJJuhsUtU99WlJJNXcRTdYh6sA7uNI0eWlfZMI,1720
PIL/PngImagePlugin.py,sha256=MpDMMPMWblQyFKADexIuNFjP45cRmrADYi819gNNmpM,48199
PIL/PpmImagePlugin.py,sha256=0df52CDB6dYk-RVO0pTd2uk1UvFd3USoBtO8azpa4HM,11746
PIL/PsdImagePlugin.py,sha256=KXU50GsaUWpDP9G--zZVjFflM5sdt9CDKCL9yzEZnyk,7838
PIL/PyAccess.py,sha256=JeLmYg6po04-ryoNQZRufMmv4wDtl1DV0AdNVFhUeXM,10189
PIL/QoiImagePlugin.py,sha256=VvipOI8cyMtlgB_8Tk7OxevxO0AadmJSj1kFMiR_ZxM,3722
PIL/SgiImagePlugin.py,sha256=aI17mrrJN6aT54UIGHvHJB0Kxg4IyRt5sSllBLvbceU,6409
PIL/SpiderImagePlugin.py,sha256=MbtvXpMFpiQuuP6KX90n6l0AYfkE_tC1v6eBdT5nYDQ,9792
PIL/SunImagePlugin.py,sha256=POGY2SGN-lFDSU6XqlsrShLEBNHgtI6ykn8_ml3sb5E,4537
PIL/TarIO.py,sha256=7VMckCocQZ-tbUngzJcFovYSH8nq-p_dKt0NLYAdsR8,1557
PIL/TgaImagePlugin.py,sha256=6nOa32pGp8llE7SzdYj82y7EiFVa4-ektCiDS1K3sjU,6830
PIL/TiffImagePlugin.py,sha256=7kvaJeMS1jjRwtsUmEEBR7XGl8Zwh_850dNq1080qDo,79211
PIL/TiffTags.py,sha256=7hsZaJPN097IFRzSHTCYjUa8GRcm3ty-lIcby80leHM,17374
PIL/WalImageFile.py,sha256=Cgn656zZJZhISDbg4-J2JAOIb8-wISJsLgrrg6mmskQ,5642
PIL/WebPImagePlugin.py,sha256=K32JaSOiIDYa7BpGlZQ5xBjlCx4vHM8ent52hKFX5MY,11732
PIL/WmfImagePlugin.py,sha256=xBbiVDKcQJqgc-c95N9Q4I6AF_ZW2JKjqt6zKpaO1Q8,4867
PIL/XVThumbImagePlugin.py,sha256=6HP8nFu5K-qE3uCx_nWhr2O4YGTmeYLLjkXmPk18rsg,2064
PIL/XbmImagePlugin.py,sha256=Q8DWHtG9tE0KDeAYGJRvjBd_Ak1C96QTLGtzDz4nPC4,2581
PIL/XpmImagePlugin.py,sha256=z0H6dU183TIOHbwzZfURc_N8oNipz6Nm-Q-s2Q_nQJM,3312
PIL/__init__.py,sha256=SOGBVqgf0bqKUcjkfXxuIuJ47LNj4ufAsE7LFGP2dJA,2091
PIL/__main__.py,sha256=hOw0dx7KqDFGy9lxphlkL6NmaCbj8lp294vXH4n35ko,44
PIL/__pycache__/BdfFontFile.cpython-37.pyc,,
PIL/__pycache__/BlpImagePlugin.cpython-37.pyc,,
PIL/__pycache__/BmpImagePlugin.cpython-37.pyc,,
PIL/__pycache__/BufrStubImagePlugin.cpython-37.pyc,,
PIL/__pycache__/ContainerIO.cpython-37.pyc,,
PIL/__pycache__/CurImagePlugin.cpython-37.pyc,,
PIL/__pycache__/DcxImagePlugin.cpython-37.pyc,,
PIL/__pycache__/DdsImagePlugin.cpython-37.pyc,,
PIL/__pycache__/EpsImagePlugin.cpython-37.pyc,,
PIL/__pycache__/ExifTags.cpython-37.pyc,,
PIL/__pycache__/FitsImagePlugin.cpython-37.pyc,,
PIL/__pycache__/FitsStubImagePlugin.cpython-37.pyc,,
PIL/__pycache__/FliImagePlugin.cpython-37.pyc,,
PIL/__pycache__/FontFile.cpython-37.pyc,,
PIL/__pycache__/FpxImagePlugin.cpython-37.pyc,,
PIL/__pycache__/FtexImagePlugin.cpython-37.pyc,,
PIL/__pycache__/GbrImagePlugin.cpython-37.pyc,,
PIL/__pycache__/GdImageFile.cpython-37.pyc,,
PIL/__pycache__/GifImagePlugin.cpython-37.pyc,,
PIL/__pycache__/GimpGradientFile.cpython-37.pyc,,
PIL/__pycache__/GimpPaletteFile.cpython-37.pyc,,
PIL/__pycache__/GribStubImagePlugin.cpython-37.pyc,,
PIL/__pycache__/Hdf5StubImagePlugin.cpython-37.pyc,,
PIL/__pycache__/IcnsImagePlugin.cpython-37.pyc,,
PIL/__pycache__/IcoImagePlugin.cpython-37.pyc,,
PIL/__pycache__/ImImagePlugin.cpython-37.pyc,,
PIL/__pycache__/Image.cpython-37.pyc,,
PIL/__pycache__/ImageChops.cpython-37.pyc,,
PIL/__pycache__/ImageCms.cpython-37.pyc,,
PIL/__pycache__/ImageColor.cpython-37.pyc,,
PIL/__pycache__/ImageDraw.cpython-37.pyc,,
PIL/__pycache__/ImageDraw2.cpython-37.pyc,,
PIL/__pycache__/ImageEnhance.cpython-37.pyc,,
PIL/__pycache__/ImageFile.cpython-37.pyc,,
PIL/__pycache__/ImageFilter.cpython-37.pyc,,
PIL/__pycache__/ImageFont.cpython-37.pyc,,
PIL/__pycache__/ImageGrab.cpython-37.pyc,,
PIL/__pycache__/ImageMath.cpython-37.pyc,,
PIL/__pycache__/ImageMode.cpython-37.pyc,,
PIL/__pycache__/ImageMorph.cpython-37.pyc,,
PIL/__pycache__/ImageOps.cpython-37.pyc,,
PIL/__pycache__/ImagePalette.cpython-37.pyc,,
PIL/__pycache__/ImagePath.cpython-37.pyc,,
PIL/__pycache__/ImageQt.cpython-37.pyc,,
PIL/__pycache__/ImageSequence.cpython-37.pyc,,
PIL/__pycache__/ImageShow.cpython-37.pyc,,
PIL/__pycache__/ImageStat.cpython-37.pyc,,
PIL/__pycache__/ImageTk.cpython-37.pyc,,
PIL/__pycache__/ImageTransform.cpython-37.pyc,,
PIL/__pycache__/ImageWin.cpython-37.pyc,,
PIL/__pycache__/ImtImagePlugin.cpython-37.pyc,,
PIL/__pycache__/IptcImagePlugin.cpython-37.pyc,,
PIL/__pycache__/Jpeg2KImagePlugin.cpython-37.pyc,,
PIL/__pycache__/JpegImagePlugin.cpython-37.pyc,,
PIL/__pycache__/JpegPresets.cpython-37.pyc,,
PIL/__pycache__/McIdasImagePlugin.cpython-37.pyc,,
PIL/__pycache__/MicImagePlugin.cpython-37.pyc,,
PIL/__pycache__/MpegImagePlugin.cpython-37.pyc,,
PIL/__pycache__/MpoImagePlugin.cpython-37.pyc,,
PIL/__pycache__/MspImagePlugin.cpython-37.pyc,,
PIL/__pycache__/PSDraw.cpython-37.pyc,,
PIL/__pycache__/PaletteFile.cpython-37.pyc,,
PIL/__pycache__/PalmImagePlugin.cpython-37.pyc,,
PIL/__pycache__/PcdImagePlugin.cpython-37.pyc,,
PIL/__pycache__/PcfFontFile.cpython-37.pyc,,
PIL/__pycache__/PcxImagePlugin.cpython-37.pyc,,
PIL/__pycache__/PdfImagePlugin.cpython-37.pyc,,
PIL/__pycache__/PdfParser.cpython-37.pyc,,
PIL/__pycache__/PixarImagePlugin.cpython-37.pyc,,
PIL/__pycache__/PngImagePlugin.cpython-37.pyc,,
PIL/__pycache__/PpmImagePlugin.cpython-37.pyc,,
PIL/__pycache__/PsdImagePlugin.cpython-37.pyc,,
PIL/__pycache__/PyAccess.cpython-37.pyc,,
PIL/__pycache__/QoiImagePlugin.cpython-37.pyc,,
PIL/__pycache__/SgiImagePlugin.cpython-37.pyc,,
PIL/__pycache__/SpiderImagePlugin.cpython-37.pyc,,
PIL/__pycache__/SunImagePlugin.cpython-37.pyc,,
PIL/__pycache__/TarIO.cpython-37.pyc,,
PIL/__pycache__/TgaImagePlugin.cpython-37.pyc,,
PIL/__pycache__/TiffImagePlugin.cpython-37.pyc,,
PIL/__pycache__/TiffTags.cpython-37.pyc,,
PIL/__pycache__/WalImageFile.cpython-37.pyc,,
PIL/__pycache__/WebPImagePlugin.cpython-37.pyc,,
PIL/__pycache__/WmfImagePlugin.cpython-37.pyc,,
PIL/__pycache__/XVThumbImagePlugin.cpython-37.pyc,,
PIL/__pycache__/XbmImagePlugin.cpython-37.pyc,,
PIL/__pycache__/XpmImagePlugin.cpython-37.pyc,,
PIL/__pycache__/__init__.cpython-37.pyc,,
PIL/__pycache__/__main__.cpython-37.pyc,,
PIL/__pycache__/_binary.cpython-37.pyc,,
PIL/__pycache__/_deprecate.cpython-37.pyc,,
PIL/__pycache__/_tkinter_finder.cpython-37.pyc,,
PIL/__pycache__/_util.cpython-37.pyc,,
PIL/__pycache__/_version.cpython-37.pyc,,
PIL/__pycache__/features.cpython-37.pyc,,
PIL/_binary.py,sha256=Ts2HKoKEMc9N4DsgIYTmJM_ecjKsexxJhsL6zR0tmuQ,2145
PIL/_deprecate.py,sha256=Azv6exyqE_fRJsRN2F155iVtRiLwLdcc3gAglT1Xr8U,2071
PIL/_imaging.cp37-win_amd64.pyd,sha256=NWFCo2OdKx3Htxp5TvPGCFqBIetyH0BholqCI1Mm7EU,2445312
PIL/_imagingcms.cp37-win_amd64.pyd,sha256=yvIH5mu0c2y2HuBuzfT0aVifRVHNtjsXgeuKkqk4ZZA,259584
PIL/_imagingft.cp37-win_amd64.pyd,sha256=RyTm70cgtLBkTWbo6XF6WSM-jXDD3jtSrPNMjnKEzyk,1722368
PIL/_imagingmath.cp37-win_amd64.pyd,sha256=vgqXTmiptl20Yc1PJWBwSiX__F9r0ucMTgHN7hkSbxE,25088
PIL/_imagingmorph.cp37-win_amd64.pyd,sha256=Rl-7gInAP21e2k84IKpJCCy1yayi7onFtPe4eHX9TAE,13824
PIL/_imagingtk.cp37-win_amd64.pyd,sha256=4WBwx19Iqmga8iOo1hoiBxzyFsqiYFv9aDhndqAU9e4,15872
PIL/_tkinter_finder.py,sha256=yTJvJBNR61MpDzWmKu_3C86QbtPIqacJkA23LGOlh0g,691
PIL/_util.py,sha256=sX8hjjr5oCOQNLChyFM7lP5ZaKIpISa4Sc0vuclsR-4,388
PIL/_version.py,sha256=EZhbGkhExLYxGRMHo0sWpmvbT_M5XAElD6bMCGY6P_k,52
PIL/_webp.cp37-win_amd64.pyd,sha256=OVAm4gDBnAF-tD7yqbdOYiJlfvVepXvbYjnNdn315-U,531456
PIL/features.py,sha256=0latlyZu0IxkkBqhnGqgbjrArAfsz8_O9OCUg4SVx2Q,9949
Pillow-9.5.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
Pillow-9.5.0.dist-info/LICENSE,sha256=xjqN48E7Qy6zoB1XXAKwTAwZ0R7wVaY1Jwq_4Z_QMqU,56523
Pillow-9.5.0.dist-info/METADATA,sha256=wQbqoMFe_7EzcL70CrvOLo23EMw8rwrvKAzdupNxaZ4,9679
Pillow-9.5.0.dist-info/RECORD,,
Pillow-9.5.0.dist-info/WHEEL,sha256=bipuClDp75Tl92wOCbNQvfy8uWiHgUDTfxkDCRx_slY,101
Pillow-9.5.0.dist-info/top_level.txt,sha256=riZqrk-hyZqh5f1Z0Zwii3dKfxEsByhu9cU9IODF-NY,4
Pillow-9.5.0.dist-info/zip-safe,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
